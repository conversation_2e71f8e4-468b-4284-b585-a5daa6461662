# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt
from buildocustom.suppliers.utils.supplier_payment_allocation import (
	allocate_payment_to_supplier_invoices,
	validate_allocation_data
)


class PaymentsPaid(Document):
	def validate(self):
		"""Validate payment data before saving"""
		if self.amount and self.supplier and self.project:
			validate_allocation_data(self)

		# Initialize unallocated amount if not set
		if not hasattr(self, 'unallocated_amount') or self.unallocated_amount is None:
			self.unallocated_amount = self.amount or 0

	def after_insert(self):
		"""Automatically allocate payment after insertion"""
		if self.amount and self.supplier and self.project:
			self.allocate_payment()

	def on_update(self):
		"""Handle payment updates"""
		# Check if key fields changed and we're not in allocation process
		if self._has_key_fields_changed() and not getattr(self, '_in_allocation', False):
			old_doc = self._doc_before_save
			old_amount = flt(old_doc.amount) if old_doc else 0
			new_amount = flt(self.amount)

			frappe.msgprint(f"Payment amount changed from {old_amount} to {new_amount}. Reallocating payment...")
			self.allocate_payment()

	def _has_key_fields_changed(self):
		"""Check if amount, supplier, or project changed"""
		if not self._doc_before_save:
			return False

		old_doc = self._doc_before_save
		amount_changed = abs(flt(self.amount) - flt(old_doc.amount)) > 0.01
		supplier_changed = self.supplier != old_doc.supplier
		project_changed = self.project != old_doc.project

		return amount_changed or supplier_changed or project_changed

	def allocate_payment(self):
		"""Perform automatic payment allocation"""
		if not self.amount or not self.supplier or not self.project:
			return

		try:
			# Set flag to prevent infinite loop
			self._in_allocation = True

			# Clear existing allocations if this is an update
			if not self.is_new():
				self._clear_existing_allocations()

			# Find outstanding invoices
			invoices = self._get_outstanding_invoices()

			if not invoices:
				# Update unallocated amount
				self.unallocated_amount = self.amount
				frappe.db.set_value("Payments Paid", self.name, "unallocated_amount", self.amount)
				frappe.msgprint(f"No outstanding invoices found. Full amount remains unallocated: {self.amount}")
				return

			# Perform FIFO allocation
			remaining_amount = flt(self.amount)
			allocation_details = []
			total_allocated = 0

			for invoice in invoices:
				if remaining_amount <= 0:
					break

				invoice_balance = flt(invoice.balance_amount)
				if invoice_balance <= 0:
					continue

				# Calculate allocation amount
				allocation_amount = min(remaining_amount, invoice_balance)

				# Create allocation record
				allocation = frappe.get_doc({
					"doctype": "Supplier Payment Allocation",
					"payments_paid": self.name,
					"supplier_invoice": invoice.name,
					"allocated_amount": allocation_amount,
					"supplier": self.supplier,
					"project": self.project,
					"allocation_date": self.date
				})
				allocation.insert(ignore_permissions=True)

				# Update invoice balance
				new_balance = invoice_balance - allocation_amount
				frappe.db.set_value("Supplier Invoices", invoice.name, "balance_amount", new_balance)

				# Update invoice status
				self._update_invoice_status(invoice.name, new_balance, flt(invoice.grand_total))

				# Add to allocation details
				allocation_details.append({
					"supplier_invoice": invoice.name,
					"invoice_date": invoice.date,
					"invoice_amount": invoice.grand_total,
					"allocated_amount": allocation_amount,
					"invoice_balance_before": invoice_balance,
					"invoice_balance_after": new_balance
				})

				remaining_amount -= allocation_amount
				total_allocated += allocation_amount

			# Update unallocated amount
			self.unallocated_amount = remaining_amount
			frappe.db.set_value("Payments Paid", self.name, "unallocated_amount", remaining_amount)

			# Update allocation details child table
			self.allocation_details = []
			for detail in allocation_details:
				self.append("allocation_details", detail)

			# Save allocation details without triggering hooks
			if allocation_details:
				frappe.db.sql("""
					DELETE FROM `tabSupplier Payment Allocation Detail`
					WHERE parent = %s
				""", self.name)

				for detail in allocation_details:
					frappe.db.sql("""
						INSERT INTO `tabSupplier Payment Allocation Detail`
						(name, parent, parenttype, parentfield, supplier_invoice, invoice_date,
						 invoice_amount, allocated_amount, invoice_balance_before, invoice_balance_after)
						VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
					""", (
						frappe.generate_hash(length=10),
						self.name,
						"Payments Paid",
						"allocation_details",
						detail["supplier_invoice"],
						detail["invoice_date"],
						detail["invoice_amount"],
						detail["allocated_amount"],
						detail["invoice_balance_before"],
						detail["invoice_balance_after"]
					))

			frappe.db.commit()
			frappe.msgprint(f"Payment allocated successfully. Allocated: {total_allocated}, Unallocated: {remaining_amount}")

		except Exception as e:
			frappe.db.rollback()
			frappe.log_error(f"Error in supplier payment allocation: {str(e)}", "Supplier Payment Allocation Error")
			frappe.throw(f"Error in supplier payment allocation: {str(e)}")
		finally:
			# Reset flag
			self._in_allocation = False

	def _get_outstanding_invoices(self):
		"""Get outstanding invoices for FIFO allocation"""
		return frappe.get_all(
			"Supplier Invoices",
			filters={
				"supplier": self.supplier,
				"project": self.project,
				"balance_amount": [">", 0],
				"docstatus": ["in", [0, 1]]  # Allow both draft (0) and submitted (1) invoices
			},
			fields=["name", "date", "grand_total", "balance_amount"],
			order_by="date asc"
		)

	def _clear_existing_allocations(self):
		"""Clear existing allocations and restore invoice balances"""
		allocations = frappe.get_all(
			"Supplier Payment Allocation",
			filters={"payments_paid": self.name},
			fields=["name", "supplier_invoice", "allocated_amount"]
		)

		if allocations:
			total_restored = 0
			restored_invoices = []

			for allocation in allocations:
				# Restore invoice balance
				current_balance = frappe.db.get_value("Supplier Invoices", allocation.supplier_invoice, "balance_amount")
				new_balance = flt(current_balance) + flt(allocation.allocated_amount)
				frappe.db.set_value("Supplier Invoices", allocation.supplier_invoice, "balance_amount", new_balance)

				# Update invoice status
				invoice_data = frappe.db.get_value("Supplier Invoices", allocation.supplier_invoice,
												 ["grand_total"], as_dict=True)
				if invoice_data:
					grand_total = flt(invoice_data.grand_total)
					self._update_invoice_status(allocation.supplier_invoice, new_balance, grand_total)

				# Track restoration details
				total_restored += flt(allocation.allocated_amount)
				restored_invoices.append(f"{allocation.supplier_invoice} (+{allocation.allocated_amount})")

				# Delete allocation record
				frappe.delete_doc("Supplier Payment Allocation", allocation.name, ignore_permissions=True)

			# Clear allocation details from child table
			frappe.db.sql("DELETE FROM `tabSupplier Payment Allocation Detail` WHERE parent = %s", self.name)

			# Log restoration details
			frappe.logger().info(f"Cleared {len(allocations)} allocations for payment {self.name}. "
							   f"Restored total amount: {total_restored}. "
							   f"Affected invoices: {', '.join(restored_invoices)}")
		else:
			frappe.logger().info(f"No existing allocations found for payment {self.name}")

	def _update_invoice_status(self, invoice_name, balance_amount, grand_total):
		"""Update invoice status based on balance"""
		if balance_amount <= 0:
			status = "Paid"
		elif balance_amount >= grand_total:
			status = "Not Paid"
		else:
			status = "Partially Paid"

		frappe.db.set_value("Supplier Invoices", invoice_name, "status", status)

	def before_delete(self):
		"""Restore invoice balances before deletion"""
		self._clear_existing_allocations()

	def on_trash(self):
		"""Additional cleanup on document deletion"""
		# The main cleanup is handled by the hook, but we can add additional logic here
		frappe.logger().info(f"Payments Paid {self.name} deleted successfully")
