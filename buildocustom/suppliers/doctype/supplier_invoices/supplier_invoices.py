# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt


class SupplierInvoices(Document):
	def validate(self):
		"""Validate and calculate balance amount considering payment allocations"""
		# Only recalculate balance if this is a new document or if grand_total has changed
		if self.is_new() or self._has_grand_total_changed():
			self._calculate_balance_with_allocations()

	def _has_grand_total_changed(self):
		"""Check if grand_total has changed significantly"""
		if not self._doc_before_save:
			return True

		old_grand_total = flt(self._doc_before_save.grand_total)
		new_grand_total = flt(self.grand_total)

		# Consider it changed if difference is more than 0.01
		return abs(old_grand_total - new_grand_total) > 0.01

	def _calculate_balance_with_allocations(self):
		"""Calculate balance amount considering existing payment allocations"""
		# Calculate base balance (grand_total)
		base_balance = flt(self.grand_total)

		# If this is a new document, set balance to base balance
		if self.is_new():
			self.balance_amount = base_balance
			self._update_status_based_on_balance()
			return

		# For existing documents, get total allocated amount
		total_allocated = frappe.db.sql("""
			SELECT COALESCE(SUM(allocated_amount), 0) as total_allocated
			FROM `tabSupplier Payment Allocation`
			WHERE supplier_invoice = %s
		""", (self.name,))[0][0]

		# Calculate balance considering allocations
		self.balance_amount = max(0, base_balance - flt(total_allocated))
		self._update_status_based_on_balance()

	def _update_status_based_on_balance(self):
		"""Update status field based on balance_amount"""
		balance = flt(self.balance_amount)
		grand_total = flt(self.grand_total)

		if balance <= 0:
			self.status = "Paid"
		elif balance < grand_total:
			self.status = "Partially Paid"
		else:
			self.status = "Not Paid"

	def on_update(self):
		"""Handle post-save operations"""
		# Ensure status is updated after save
		if hasattr(self, '_status_needs_update'):
			self._update_status_based_on_balance()
			delattr(self, '_status_needs_update')
