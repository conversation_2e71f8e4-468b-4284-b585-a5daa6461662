# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class BOQ(Document):
	def on_update(self):
		"""Update project progress and total value when BOQ is updated"""
		if self.project:
			self.update_project_calculations()

	def on_trash(self):
		"""Update project progress and total value when BOQ is deleted"""
		if self.project:
			self.update_project_calculations()

	def update_project_calculations(self):
		"""Update the progress percentage and total value for the linked project"""
		try:
			project_doc = frappe.get_doc("Projects", self.project)

			# Update total value if contract type is Item Rate
			if project_doc.contract_type == "Item Rate":
				project_doc.calculate_total_value()

			# Update progress
			project_doc.calculate_progress()
			project_doc.save(ignore_permissions=True)
		except Exception as e:
			frappe.log_error(f"Error updating project calculations from BOQ {self.name}: {str(e)}")
