// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Petty Cash", {
	refresh(frm) {
		// Calculate totals on form refresh
		calculate_totals(frm);
	},

	onload(frm) {
		// Calculate totals when form loads
		calculate_totals(frm);
	},

	opening_balance(frm) {
		// Trigger calculation when opening balance changes
		calculate_totals(frm);
	}
});

// Event handlers for child table changes
frappe.ui.form.on("Petty Cash Table", {
	receive_amt(frm, cdt, cdn) {
		// Trigger calculation when receive amount changes
		calculate_totals(frm);
	},

	exp_amt(frm, cdt, cdn) {
		// Trigger calculation when expense amount changes
		calculate_totals(frm);
	},

	table_zznz_add(frm, cdt, cdn) {
		// Trigger calculation when new row is added
		calculate_totals(frm);
	},

	table_zznz_remove(frm, cdt, cdn) {
		// Trigger calculation when row is removed
		calculate_totals(frm);
	}
});

// Function to calculate totals
function calculate_totals(frm) {
	let total_received = 0;
	let total_expense = 0;

	// Sum up amounts from the child table
	if (frm.doc.table_zznz) {
		frm.doc.table_zznz.forEach(function(row) {
			if (row.receive_amt) {
				total_received += row.receive_amt;
			}
			if (row.exp_amt) {
				total_expense += row.exp_amt;
			}
		});
	}

	// Get opening balance
	let opening_balance = frm.doc.opening_balance || 0;

	// Update the calculated fields
	frm.set_value("total_petty_cash_received", total_received);
	frm.set_value("total_expense", total_expense);
	frm.set_value("balance_in_hand", opening_balance + total_received - total_expense);
}
