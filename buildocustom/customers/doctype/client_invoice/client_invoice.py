# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class ClientInvoice(Document):
	def validate(self):
		"""Validate and calculate balance amount considering payment allocations"""
		# Only recalculate balance if this is a new document or if net_payable has changed
		if self.is_new() or self._has_net_payable_changed():
			self._calculate_balance_with_allocations()

	def _has_net_payable_changed(self):
		"""Check if net_payable has changed significantly"""
		if not self._doc_before_save:
			return True

		old_net_payable = frappe.utils.flt(self._doc_before_save.net_payable)
		new_net_payable = frappe.utils.flt(self.net_payable)

		# Consider it changed if difference is more than 0.01
		return abs(old_net_payable - new_net_payable) > 0.01

	def _calculate_balance_with_allocations(self):
		"""Calculate balance amount considering existing payment allocations"""
		from frappe.utils import flt

		# Calculate base balance (net_payable - tds_amount)
		base_balance = flt(self.net_payable) - flt(self.tds_amount)

		# If this is a new document, set balance to base balance
		if self.is_new():
			self.balance_amount = base_balance
			return

		# For existing documents, get total allocated amount
		total_allocated = frappe.db.sql("""
			SELECT COALESCE(SUM(allocated_amount), 0) as total_allocated
			FROM `tabPayment Allocation`
			WHERE client_invoice = %s
		""", (self.name,))[0][0]

		# Calculate balance considering allocations
		self.balance_amount = max(0, base_balance - flt(total_allocated))


@frappe.whitelist()
def get_invoices_for_customer_project(customer, project):
	"""
	Get all invoices for a specific customer and project
	
	Args:
		customer (str): Customer name
		project (str): Project name
		
	Returns:
		list: List of invoices
	"""
	invoices = frappe.get_all(
		"Client Invoice",
		filters={
			"customer": customer,
			"project": project,
			"docstatus": ["!=", 2]  # Not cancelled
		},
		fields=["name", "invoice_no", "date"]
	)
	
	return invoices


@frappe.whitelist()
def get_invoice_items(invoice_name, target_table):
	"""
	Get items from a specific invoice along with bank details

	Args:
		invoice_name (str): Invoice name
		target_table (str): Target table name (built_up_items or item_rate_items)

	Returns:
		dict: Dictionary containing items and bank details
	"""
	if target_table == "built_up_items":
		items = frappe.get_all(
			"Builtup Invoice Items",
			filters={"parent": invoice_name},
			fields=[
				"hsn_code", "desc", "total_perc", "prev_perc",
				"curr_perc", "pre_bill_amt", "curr_bill_amt"
			]
		)
	elif target_table == "item_rate_items":
		items = frappe.get_all(
			"Item Rate Invoice Items",
			filters={"parent": invoice_name},
			fields=[
				"hsn_code", "desc", "uom", "qty", "rate", "amount",
				"pre_bill_qty", "pre_bill_amt", "curr_bill_qty", "curr_bill_amt"
			]
		)
	else:
		items = []

	# Get bank details from the source invoice
	bank_details = get_bank_details(invoice_name)

	return {
		"items": items,
		"bank_details": bank_details
	}


@frappe.whitelist()
def get_bank_details(invoice_name=None):
	"""
	Get bank details for populating in Client Invoice

	Args:
		invoice_name (str, optional): Invoice name to get bank details from

	Returns:
		dict: Bank details
	"""
	bank_details = {}

	if invoice_name:
		# Try to get bank details from the source invoice
		try:
			source_invoice = frappe.get_doc("Client Invoice", invoice_name)
			bank_details = {
				"bank_name": source_invoice.get("bank_name", ""),
				"bank_acc_no": source_invoice.get("bank_acc_no", ""),
				"acc_type": source_invoice.get("acc_type", ""),
				"bank_branch": source_invoice.get("bank_branch", ""),
				"ifsc_code": source_invoice.get("ifsc_code", ""),
				"pan_no_b": source_invoice.get("pan_no_b", ""),
				"gst_no_b": source_invoice.get("gst_no_b", "")
			}
		except Exception:
			# If source invoice doesn't exist or has no bank details, use defaults
			bank_details = get_default_bank_details()
	else:
		# Get default bank details
		bank_details = get_default_bank_details()

	return bank_details


def get_default_bank_details():
	"""
	Get default bank details from system settings or hardcoded values

	Returns:
		dict: Default bank details
	"""
	# You can modify these default values or fetch from a settings DocType
	default_details = {
		"bank_name": "State Bank of India",
		"bank_acc_no": "**********",
		"acc_type": "Current Account",
		"bank_branch": "Main Branch",
		"ifsc_code": "SBIN0001234",
		"pan_no_b": "**********",
		"gst_no_b": "27**********1Z5"
	}

	# Try to get from system settings if available
	try:
		# Check if there's a Company Settings or similar DocType
		# You can modify this to fetch from your actual settings DocType
		settings = frappe.get_single("System Settings")  # Replace with your settings DocType
		if hasattr(settings, 'bank_name'):
			default_details.update({
				"bank_name": getattr(settings, 'bank_name', default_details["bank_name"]),
				"bank_acc_no": getattr(settings, 'bank_acc_no', default_details["bank_acc_no"]),
				"acc_type": getattr(settings, 'acc_type', default_details["acc_type"]),
				"bank_branch": getattr(settings, 'bank_branch', default_details["bank_branch"]),
				"ifsc_code": getattr(settings, 'ifsc_code', default_details["ifsc_code"]),
				"pan_no_b": getattr(settings, 'pan_no_b', default_details["pan_no_b"]),
				"gst_no_b": getattr(settings, 'gst_no_b', default_details["gst_no_b"])
			})
	except Exception:
		# If no settings found, use hardcoded defaults
		pass

	return default_details
