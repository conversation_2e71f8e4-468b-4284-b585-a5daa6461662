# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt


class PaymentAllocation(Document):
	def before_save(self):
		"""Set customer and project from payment received"""
		if self.payment_received:
			payment_doc = frappe.get_doc("Payment Received", self.payment_received)
			self.customer = payment_doc.customer
			self.project = payment_doc.project

		if not self.allocation_date:
			self.allocation_date = frappe.utils.now()

	pass